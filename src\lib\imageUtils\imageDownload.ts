/**
 * 图片下载工具函数
 * 支持单张和批量图片下载
 */

import type { SupportedFormat } from '@/lib/imageUtils/imageConvert';
import {
  processImagePipeline,
  type ImageProcessingConfig,
} from '@/lib/imageUtils/imageProcessingPipeline';

/**
 * 下载单张图片
 * @param imageUrl 图片URL (base64 或 blob URL)
 * @param fileName 完整文件名（包含扩展名）
 * @param format 图片格式（用于确保文件名有正确扩展名）
 */
export async function downloadSingleImage(
  imageUrl: string,
  fileName: string,
  format: SupportedFormat = 'png'
): Promise<void> {
  try {
    // 确保文件名有扩展名
    const finalFileName = fileName.includes('.')
      ? fileName
      : `${fileName}.${format}`;

    // 如果是base64格式，直接下载
    if (imageUrl.startsWith('data:')) {
      const link = document.createElement('a');
      link.href = imageUrl;
      link.download = finalFileName;
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
      return;
    }

    // 如果是URL，先fetch获取blob
    const response = await fetch(imageUrl);
    if (!response.ok) {
      throw new Error(`Failed to fetch image: ${response.statusText}`);
    }

    const blob = await response.blob();
    const objectUrl = URL.createObjectURL(blob);

    const link = document.createElement('a');
    link.href = objectUrl;
    link.download = finalFileName;
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);

    // 清理URL对象
    URL.revokeObjectURL(objectUrl);
  } catch (error) {
    console.error('下载图片失败:', error);
    throw new Error(
      `下载失败: ${error instanceof Error ? error.message : '未知错误'}`
    );
  }
}

/**
 * 批量下载图片（打包为ZIP）
 * @param images 图片数组
 * @param zipFileName ZIP文件名
 * @param onProgress 进度回调
 */
export async function downloadImagesAsZip(
  images: Array<{
    url: string;
    name: string;
    format?: SupportedFormat;
  }>,
  zipFileName: string = 'images',
  onProgress?: (current: number, total: number) => void
): Promise<void> {
  // 动态导入JSZip（避免增加bundle大小）
  const JSZip = (await import('jszip')).default;

  const zip = new JSZip();
  const total = images.length;

  try {
    for (let i = 0; i < images.length; i++) {
      const image = images[i];

      // 更新进度
      if (onProgress) {
        onProgress(i, total);
      }

      try {
        let blob: Blob;

        if (image.url.startsWith('data:')) {
          // 处理base64格式
          const response = await fetch(image.url);
          blob = await response.blob();
        } else {
          // 处理URL格式
          const response = await fetch(image.url);
          if (!response.ok) {
            console.warn(
              `Failed to fetch image ${image.name}: ${response.statusText}`
            );
            continue;
          }
          blob = await response.blob();
        }

        // 确定文件扩展名
        const format = image.format || 'png';
        const fileName = image.name.includes('.')
          ? image.name
          : `${image.name}.${format}`;

        // 添加到ZIP
        zip.file(fileName, blob);
      } catch (error) {
        console.warn(`Failed to process image ${image.name}:`, error);
        // 继续处理其他图片
      }
    }

    // 完成进度
    if (onProgress) {
      onProgress(total, total);
    }

    // 生成ZIP文件
    const zipBlob = await zip.generateAsync({
      type: 'blob',
      compression: 'DEFLATE',
      compressionOptions: {
        level: 6, // 平衡压缩率和速度
      },
    });

    // 下载ZIP文件
    const objectUrl = URL.createObjectURL(zipBlob);
    const link = document.createElement('a');
    link.href = objectUrl;
    link.download = `${zipFileName}.zip`;
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);

    // 清理URL对象
    URL.revokeObjectURL(objectUrl);
  } catch (error) {
    console.error('批量下载失败:', error);
    throw new Error(
      `批量下载失败: ${error instanceof Error ? error.message : '未知错误'}`
    );
  }
}

/**
 * 将背景颜色或背景图片与去背后的图片合成
 * @param processedImageUrl 去背后的图片URL
 * @param backgroundColor 背景颜色
 * @param backgroundImageUrl 背景图片URL
 * @param format 输出格式 (默认 'png')
 * @returns 合成后的图片数据URL
 */
export async function compositeImageWithBackground(
  processedImageUrl: string,
  backgroundColor?: string,
  backgroundImageUrl?: string,
  format: string = 'png'
): Promise<string> {
  return new Promise((resolve, reject) => {
    const img = new Image();
    img.crossOrigin = 'anonymous';

    img.onload = async () => {
      const canvas = document.createElement('canvas');
      canvas.width = img.naturalWidth;
      canvas.height = img.naturalHeight;
      const ctx = canvas.getContext('2d');

      if (!ctx) {
        reject(new Error('无法获取画布上下文'));
        return;
      }

      // 1. 绘制背景
      if (backgroundImageUrl) {
        try {
          const bgImg = new Image();
          bgImg.crossOrigin = 'anonymous';

          await new Promise<void>((bgResolve, bgReject) => {
            bgImg.onload = () => {
              // 以 cover 方式绘制背景图片
              const imgAspect = bgImg.naturalWidth / bgImg.naturalHeight;
              const canvasAspect = canvas.width / canvas.height;
              let sx, sy, sWidth, sHeight;

              if (imgAspect > canvasAspect) {
                sHeight = bgImg.naturalHeight;
                sWidth = sHeight * canvasAspect;
                sx = (bgImg.naturalWidth - sWidth) / 2;
                sy = 0;
              } else {
                sWidth = bgImg.naturalWidth;
                sHeight = sWidth / canvasAspect;
                sx = 0;
                sy = (bgImg.naturalHeight - sHeight) / 2;
              }

              ctx.drawImage(
                bgImg,
                sx,
                sy,
                sWidth,
                sHeight,
                0,
                0,
                canvas.width,
                canvas.height
              );
              bgResolve();
            };
            bgImg.onerror = bgReject;
            bgImg.src = backgroundImageUrl;
          });
        } catch (error) {
          console.warn('背景图片加载失败，使用透明背景:', error);
        }
      } else if (backgroundColor && backgroundColor !== 'transparent') {
        ctx.fillStyle = backgroundColor;
        ctx.fillRect(0, 0, canvas.width, canvas.height);
      }

      // 2. 绘制前景图片（去背后的图片）
      ctx.drawImage(img, 0, 0);

      // 3. 返回合成后的图片数据
      const mimeType = format === 'jpg' ? 'image/jpeg' : `image/${format}`;
      const quality = format === 'jpg' || format === 'jpeg' ? 0.9 : undefined;
      resolve(canvas.toDataURL(mimeType, quality));
    };

    img.onerror = () => reject(new Error('图片加载失败'));
    img.src = processedImageUrl;
  });
}

/**
 * 确定图片的处理状态和最佳下载URL
 * 使用统一的处理管道，将所有操作组合应用
 * @param image 图片状态对象
 * @returns 下载信息
 */
export async function getImageDownloadInfo(image: {
  name: string;
  previewUrl: string;
  processedUrl?: string | null;
  resizedUrl?: string | null;
  convertedUrl?: string | null;
  compressedUrl?: string | null;
  targetWidth?: number;
  targetHeight?: number;
  resizeMode?: 'fit' | 'fill' | 'stretch';
  backgroundColor?: string;
  backgroundImageUrl?: string;
  convertedFormat?: string;
  originalFormat?: string;
  compressionLevel?: 'original' | 'light' | 'medium' | 'deep';
  customCompressionSize?: number;
  customCompressionUnit?: 'KB' | 'MB';
}) {
  try {
    // 检查是否需要进行任何处理
    const needsProcessing =
      (image.backgroundColor && image.backgroundColor !== 'transparent') ||
      image.backgroundImageUrl ||
      (image.targetWidth && image.targetHeight) ||
      image.convertedFormat ||
      image.compressionLevel ||
      (image.customCompressionSize && image.customCompressionUnit);

    // 如果不需要任何处理，直接返回最高优先级的URL
    if (!needsProcessing) {
      let url = image.previewUrl;
      if (image.compressedUrl) url = image.compressedUrl;
      else if (image.convertedUrl) url = image.convertedUrl;
      else if (image.resizedUrl) url = image.resizedUrl;
      else if (image.processedUrl) url = image.processedUrl;

      return {
        url,
        fileName: image.name,
        dimensions:
          image.targetWidth && image.targetHeight
            ? `${image.targetWidth}x${image.targetHeight}`
            : null,
        format: image.convertedFormat || image.originalFormat || 'png',
      };
    }

    // 构建处理配置
    const config: ImageProcessingConfig = {
      backgroundColor: image.backgroundColor,
      backgroundImageUrl: image.backgroundImageUrl,
      targetWidth: image.targetWidth,
      targetHeight: image.targetHeight,
      resizeMode: image.resizeMode || 'fit',
      outputFormat: (image.convertedFormat ||
        image.originalFormat ||
        'png') as SupportedFormat,
      compressionLevel: image.compressionLevel,
      customCompressionSize: image.customCompressionSize,
      customCompressionUnit: image.customCompressionUnit,
    };

    // 使用统一处理管道生成最终图片
    const result = await processImagePipeline(
      image.previewUrl,
      image.processedUrl || null,
      config
    );

    return {
      url: result.dataUrl,
      fileName: image.name,
      dimensions: `${result.width}x${result.height}`,
      format: result.format,
    };
  } catch (error) {
    console.error(`图片 ${image.name} 处理失败:`, error);

    // 处理失败时回退到原有逻辑
    let url = image.previewUrl;
    if (image.compressedUrl) url = image.compressedUrl;
    else if (image.convertedUrl) url = image.convertedUrl;
    else if (image.resizedUrl) url = image.resizedUrl;
    else if (image.processedUrl) url = image.processedUrl;

    return {
      url,
      fileName: image.name,
      dimensions:
        image.targetWidth && image.targetHeight
          ? `${image.targetWidth}x${image.targetHeight}`
          : null,
      format: image.convertedFormat || image.originalFormat || 'png',
    };
  }
}
